import React, {useEffect, useReducer, useState} from "react"
import {
    Button,
    Flex,
    MenuProps,
    Pagination,
    Popconfirm,
    Rate,
    Space,
    Table,
    Tooltip,
    Input,
    DatePicker,
    List
} from "antd"
import type { DatePickerProps } from 'antd';
import Column from "antd/es/table/Column"
import type {TableRowSelection} from "antd/es/table/interface"
import MultiRowMenus from "../common/MultiRowMenus.tsx";
import {PlusOutlined, StarOutlined} from "@ant-design/icons";
import MonsterCardForm from "./MonsterCardForm.tsx";
import SpellCardForm from "./SpellCardForm.tsx";
import TrapCardForm from "./TrapCardForm.tsx";
import BuyForm from "./CardBuyForm.tsx";
import CopyForm from "./CardCopyForm.tsx";
import {delete_password_guard, list_cards, list_date_buy_sum, list_date_buy_detail, update_card_favorite} from "../common/BackendService.tsx";
import {YgoCard, CardColor} from "./Def.tsx";

const { Search } = Input;

const menuProps: MenuProps['items'] = [
    {
        label: 'Submit and continue',
        key: '1',
    },
];

const CardManage: React.FC = () => {
    const [data, setData] = useState<YgoCard[]>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
    const [total, setTotal] = useState<number>(0);
    const [totalBuyCount, setTotalBuyCount] = useState<number>(0);
    const [totalBuyPrice, setTotalBuyPrice] = useState<number>(0);
    const [query, setQuery] = useState({keyword: undefined, buyDate: undefined});
    const [pagination, setPagination] = useState({current: 1, pageSize: 10})
    const [sorter, setSorter] = useState({field: '', order: ''})
    const [footer, setFooter] = useState([])
    const [buySumMap, setBuySumMap] = useState({})
    const [buyDetailList, setBuyDetailList] = useState([])

    // 特殊的日期样式
    const specialDateCellStyle: React.CSSProperties = {
        border: `1px solid red`,
        borderRadius: '50%',
        color: `red`,
    };

    // 月选择面板修改时获取当月日期的购买汇总数据刷新
    const buyDatePanelChange = (value: any, mode: any) => {
        if ('date' === mode) {
            list_date_buy_sum({ monthId: value.format('YYYY-MM') })
                .then((res) => {
                    if (res.success && res.data) {
                        setBuySumMap(res.data);
                    }
                })
        }
    }

    // 日期选择器的个性化渲染方法
    const buyDateCellRender: DatePickerProps['cellRender'] = (current, info) => {
        if (info.type !== 'date') {
            return info.originNode;
        }
        if (typeof current === 'number' || typeof current === 'string') {
            return <div className="ant-picker-cell-inner">{current}</div>;
        }

        const buyDate = current.format("YYYY-MM-DD")
        const buySum = buySumMap[buyDate]
        const titleNode = buySum ? (<Tooltip title={dayDetail} placement="right" overlayStyle={{maxWidth: '580px'}} color={'white'}>
            <Button type="dashed" danger>
                购买数量：{buySum.buyCount}； 购买金额：{buySum.buyPrice.toFixed(2)}
            </Button>
        </Tooltip>) : "";
        return (
            <div className="ant-picker-cell-inner" style={buySum ? specialDateCellStyle : {}}
                 onMouseOver={() => buySum ? loadDayBuyDetail(buyDate) : null}
            >
                {buySum ? <Tooltip title={titleNode} overlayStyle={{maxWidth: '300px'}} mouseLeaveDelay={0.5} color={'white'}>
                    {current.date()}
                </Tooltip> : current.date()}
            </div>
        );
    };

    const dayDetail =
        <div style={{width: '500px'}}>
            <List
                className="demo-loadmore-list"
                itemLayout="horizontal"
                dataSource={buyDetailList}
                renderItem={(item, index) => (
                    <List.Item>
                        <List.Item.Meta
                            title={(index + 1) + '、' + item.cardName + ('（' + item.cardRarity + '）')}
                            description={false}
                        />
                        <div>{item.buyPrice.toFixed(2)}</div>
                    </List.Item>
                )}
            /></div>

    const loadDayBuyDetail = (buyDate: any) => {
        list_date_buy_detail({ 'buyDate': buyDate })
            .then((res) => {
                if (res.success && res.data) {
                    setBuyDetailList(res.data['rows'] || []);
                }
            })
    }

    function reducer(state: any, action: any) {
        switch (action.type) {
            case 'add_monster_form': {
                return { ...state, monsterFormOpened: true, monsterForm: undefined };
            }
            case 'close_monster_form': {
                return { ...state, monsterFormOpened: false };
            }
            case 'set_multi': {
                return { ...state, multiEnabled: !!action.multiEnabled };
            }
            case 'add_spell_form': {
                return { ...state, spellFormOpened: true, spellForm: undefined };
            }
            case 'close_spell_form': {
                return { ...state, spellFormOpened: false };
            }
            case 'add_trap_form': {
                return { ...state, trapFormOpened: true, trapForm: undefined };
            }
            case 'close_trap_form': {
                return { ...state, trapFormOpened: false };
            }
            case 'open_buy_form': {
                return { ...state, buyFormOpened: true, buyForm: action.buyForm };
            }
            case 'close_buy_form': {
                return { ...state, buyFormOpened: false };
            }
            case 'open_sell_form': {
                return { ...state, sellFormOpened: true, sellForm: action.sellForm }
            }
            case 'close_sell_form': {
                return { ...state, sellFormOpened: false };
            }
            case 'add_copy_form': {
                return { ...state, copyFormOpened: true, copyForm: action.copyForm };
            }
            case 'close_copy_form': {
                return { ...state, copyFormOpened: false, copyForm: undefined};
            }
            default:
                throw new Error();
        }
    }

    const [state, dispatch] = useReducer(reducer, { record: null, open: false, multiEnabled: false });

    useEffect(() => {
        fetchData()
    }, [sorter, pagination, query]);

    const fetchData = () => {
        try {
            list_cards(Object.assign({pagination: {page: pagination.current, rows: pagination.pageSize}, sortField: sorter.field, sortOrder: sorter.order}, query))
                .then((result) => {
                    const list: YgoCard[] = result.data['rows']
                    setData(list.map(item => ({ ...item, key: item.id })));
                    setTotal(result.data['total'])

                    const extra = result.data['extra']
                    if (extra) {
                        setFooter(extra.subtotal)
                        setTotalBuyCount(extra.total.totalBuyCount)
                        setTotalBuyPrice(extra.total.totalBuyPrice)
                    }
                })
        } catch (error) {
            console.error("Error fetching data: ", error);
        }
    };

    const rowSelection: TableRowSelection<YgoCard> = state.multiEnabled ? {
        fixed: true,
        selectedRowKeys,
        onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
    } : null

    const multiEnabledChanged = (e: any) => {
        dispatch({type: 'set_multi', multiEnabled: e.target.checked})
    }

    const openMonsterForm = () => {
        dispatch({type: 'add_monster_form'})
    }

    const closeMonsterForm = () => {
        dispatch({type: 'close_monster_form'})
        fetchData()
    }

    const openSpellForm = () => {
        dispatch({type: 'add_spell_form'})
    }

    const closeSpellForm = () => {
        dispatch({type: 'close_spell_form'})
        fetchData()
    }

    const openTrapForm = () => {
        dispatch({type: 'add_trap_form'})
    }

    const closeTrapForm = () => {
        dispatch({type: 'close_trap_form'})
        fetchData()
    }

    const doBuy = (record: YgoCard) => {
        dispatch({type: 'open_buy_form', buyForm: record})
    }

    const closeBuyForm = () => {
        dispatch({type: 'close_buy_form'})
        fetchData()
    }

    const doSell = (record: YgoCard) => {
        dispatch({type: 'open_sell_form', sellForm: record})
    }

    const closeSellForm = () => {
        dispatch({type: 'close_sell_form'})
        fetchData()
    }

    const doCopy = (record: YgoCard) => {
        dispatch({type: 'add_copy_form', copyForm: {cardId: record.id, cardNumber: record.cardNumber}})
    }

    const closeCopyForm = () => {
        dispatch({type: 'close_copy_form'})
        fetchData()
    }

    const doDelete = (record: YgoCard) => {
        delete_password_guard(record.id)
            .then((res) => {
                if (res.success) {
                    fetchData()
                }
            })
    }

    const favoriteOnChange = (value, record) => {
        update_card_favorite({cardId: record.id, favorite: value})
    }

    const onSearch = (keyword: String) => {
        if (keyword != query.keyword) {
            setPagination({ ...pagination, current: 1 });
            setQuery({ ...query, keyword })
        }
    }

    const buyDateOnChange = (buyDate: any) => {
        setQuery({ ...query, buyDate: buyDate ? buyDate.format('YYYY-MM-DD') : '' })
    }

    const tableOnChange = (_1: any, _2: any, s, e) => {
        if (e && e.action === 'sort') {
            setSorter({field: s.field || '', order: ('descend' === s.order ? 'desc' : '')})
        }
    }

    return (
        <div style={{ display: 'flex', flexDirection: 'column', height: '100%'}}>
            <div style={{ flex: 1, overflow: 'auto' }}>
                <Flex gap="middle" vertical style={{padding: '10px'}} >
                    <Space>
                        <Search key="keyword" defaultValue={query.keyword} placeholder="请输入关键字进行查询" onSearch={onSearch} allowClear={true} />
                        <DatePicker format="YYYY-MM-DD" key="buyDate" defaultValue={query.buyDate} placeholder="购入日期" onChange={buyDateOnChange} onPanelChange={buyDatePanelChange} cellRender={buyDateCellRender} />

                        <MultiRowMenus menuProps={ menuProps } onMultiEnabledChange={multiEnabledChanged} />

                        <Button ghost type="primary" icon={<PlusOutlined />} onClick={openMonsterForm}>召唤伙伴</Button>
                        <MonsterCardForm key="monsterForm" init={state.monsterForm} open={state.monsterFormOpened} onClose={closeMonsterForm} />

                        <Button ghost type="default" style={{color: 'green', borderColor: 'green'}} icon={<PlusOutlined />} onClick={openSpellForm}>研习魔法</Button>
                        <SpellCardForm key="spellForm" init={state.spellForm} open={state.spellFormOpened} onClose={closeSpellForm} />

                        <Button ghost danger={true} icon={<PlusOutlined />} onClick={openTrapForm}>设置陷阱</Button>
                        <TrapCardForm key="trapForm" init={state.trapForm} open={state.trapFormOpened} onClose={closeTrapForm} />

                        <BuyForm key="buyForm" init={state.buyForm} open={state.buyFormOpened} onClose={closeBuyForm} />
                        <BuyForm key="sellForm" init={state.sellForm} open={state.sellFormOpened} onClose={closeSellForm} />
                        <CopyForm key="copyForm" init={state.copyForm} open={state.copyFormOpened} onClose={closeCopyForm} />
                    </Space>
                </Flex>
                <Table<YgoCard> dataSource={data} rowSelection={rowSelection} pagination={false} onChange={tableOnChange}
                                footer={() =>
                                    <div>
                                        {footer.map(item => <span
                                            style={CardColor[item.cardType]}>{item.cardTypeLabel}：{item.totalBuyCount}；</span>)}
                                        <span>总数：{totalBuyCount}；总价：{totalBuyPrice}元；</span>
                                    </div>
                                }>
                    <Column title=""
                            dataIndex={undefined}
                            key="index"
                            width="60px"
                            align="center"
                            fixed="left"
                            render={(_: any, _data: YgoCard, index: number) => index + 1}
                    ></Column>

                    <Column title="重要"
                            dataIndex="favorite"
                            key="index"
                            width="60px"
                            align="center"
                            fixed="left"
                            render={(value: any, record: YgoCard) => <Rate defaultValue={value ? 1: 0} onChange={(value) => favoriteOnChange(value, record)} count={1} character={<StarOutlined />} />}
                    ></Column>

                    <Column title="卡名" dataIndex="cardName" key="cardName" width="250px" fixed="left"
                            render={(_: any, record: YgoCard) => (
                                <span style={CardColor[record.cardType]}>{record.cardName}</span>
                            )}
                    ></Column>
                    <Column title="卡号" dataIndex="cardNumber" key="cardNumber" width="150px" fixed="left"></Column>
                    <Column title="种类" dataIndex="cardTypeLabel" key="cardTypeLabel" width="80px" align="center"></Column>
                    <Column title="罕贵度" dataIndex="cardRarity" key="cardRarity" width="120px" sorter={true}></Column>
                    <Column title="数量" dataIndex="buyCount" key="buyCount" width="80px" align="center" ></Column>
                    <Column title="单价" dataIndex="buyPrice" key="buyPrice" width="100px" align="center" sorter={true}
                            render={(text) => (<div style={{textAlign:'right'}}>{text ? text.toFixed(2) : ''}</div>)}
                    ></Column>
                    <Column title="描述" dataIndex="cardDescription" key="cardDescription" ellipsis={{ showTitle: false }}
                            render={(value: any) => (
                                <Tooltip overlayStyle={{maxWidth: '580px'}} placement="topLeft" title={<div style={{whiteSpace: 'pre-wrap', wordWrap: 'break-word'}}>{value}</div>}>
                                    {value}
                                </Tooltip>
                            )}
                    ></Column>
                    <Column width="200px"
                            title="操作"
                            key="action"
                            fixed="right"
                            render={(_: any, record: YgoCard) => (
                                <Space.Compact size="small">

                                    <Button type="link" key="buy" size="small" onClick={() => doBuy(record)}>购入</Button>
                                    <Button type="link" key="sell" size="small" onClick={() => doSell(record)}>售出</Button>
                                    <Button type="link" key="copy" size="small" onClick={() => doCopy(record)}>异罕</Button>

                                    <Popconfirm
                                        title="请确认是否要删除这条记录吗？"
                                        description="删除这条记录！"
                                        okText="朕准了"
                                        cancelText="手抖了"
                                        placement="left"
                                        onConfirm={() => doDelete(record)}
                                    >
                                        <Button type="link" key="del" size="small" danger>删除</Button>
                                    </Popconfirm>
                                </Space.Compact>
                            )}
                    />
                </Table>
            </div>

            {/* 分页组件 */}
            <div style={{ textAlign: 'center', padding: '10px 10px' }}>
                <Pagination
                    align="center"
                    pageSize={pagination.pageSize}
                    current={pagination.current}
                    total={total}
                    onChange={(page, pageSize) => {
                        setPagination({ ...pagination, current: page, pageSize: pageSize });
                    }}
                />
            </div>
        </div>
    )
}
export default CardManage;