import React, {useEffect} from "react";
import {Button, DatePicker, Form, FormProps, Input, InputNumber, message, Modal, Select} from "antd";
import {add_card_buy, ResponseData} from "../common/BackendService.tsx";
import {baseDataHolder} from "../../provider/BaseDataProvider.tsx";
import {CardSell} from "./Def.tsx";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";

interface CardSellFormProps {
    open?: boolean
    onClose?: () => void
    init?: CardSell
}

const CardSellForm: React.FC<CardSellFormProps> = ({open = false, onClose, init = null}) => {

    // form引用
    const [form] = Form.useForm<CardSell>();

    // 初始化form
    useEffect(() => {
        if (open) {
            if (null != init) {
                form.resetFields()
                form.setFieldsValue({cardId: init.id, cardName: init.cardName})
                form.setFieldsValue({appearance: 'B', vendor: '日厂', buyPlatform: '集换社'})
            } else {
                form.resetFields()
            }
        }
    }, [open, init]);

    // 基础数据
    const { baseData } = baseDataHolder();

    // 消息
    const [messageApi, contextHolder] = message.useMessage();

    const onFinishFailed: FormProps<CardSell>['onFinishFailed'] = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    const onFinish: FormProps<CardSell>['onFinish'] = async (values) => {
        add_card_buy(values)
            .then((res: ResponseData) => {
                if (res?.success) {
                    onClose()
                    messageApi.info('添加成功。');
                } else {
                    messageApi.error('添加失败：' + res?.message);
                }
            })
            .catch(err => {
                if (err?.response?.data?.message) {
                    messageApi.info(err?.response?.data?.message)
                } else {
                    messageApi.error('服务器处理出错，请稍候再重试。')
                }
            });
    };

    // 提交form
    const submitForm = async () => {
        try {
            const values = await form.validateFields();
            console.log("form", values)
            onFinish(values);
        } catch (errorInfo) {
            onFinishFailed(errorInfo);
        }
    };

    // 自定义窗口底部工具栏
    const customFooter = (
        <>
            <Button key="cancel" onClick={onClose}>
                取消
            </Button>
            <Button key="submit" type="primary" onClick={submitForm}>
                保存
            </Button>
        </>
    );

    return (
        <Modal
            title="售出信息"
            open={open}
            onCancel={onClose}
            footer={customFooter}
            width="80%"
            style={{ maxWidth: 1000 }}
        >
            {contextHolder}
            <Form<CardSell>
                name="basic"
                form={form}
                labelCol={{span: 8}}
                wrapperCol={{span: 16}}
                style={{maxWidth: 600, marginTop: '20px'}}
                initialValues={{remember: true}}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
            >

                <Form.Item
                    name="id"
                    style={{ display: 'none' }}
                >
                    <Input type="hidden" />
                </Form.Item>

                <Form.Item
                    name="cardId"
                    style={{ display: 'none' }}
                >
                    <Input type="hidden" />
                </Form.Item>

                <Form.Item
                    label="卡名"
                    name="cardName"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                >
                    <Input disabled={true}/>
                </Form.Item>

                <Form.Item
                    label="购买价格"
                    name="buyPrice"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                    rules={[{required: true, message: '请填写购买的价格!'}]}
                >
                    <InputNumber />
                </Form.Item>
                
                <Form.Item
                    label="购买日期"
                    name="buyDate"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                    rules={[{required: true, message: '请选择购买日期!'}]}
                    getValueFromEvent={(...[, dateString]) => dateString }
                    getValueProps={value => ({
                        value: value ? dayjs(value) : undefined
                    })}
                >
                    <DatePicker />
                </Form.Item>

                <Form.Item
                    label="品相自估等级"
                    name="appearance"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                >
                    <Select options={[{value: 'S', label:'极美品'}, {value: 'A', label:'美品'}, {value: 'B', label:'流通'}, {value: 'C', label:'瑕疵'}, {value: 'D', label:'残次'}]}
                    />
                </Form.Item>

                <Form.Item
                    label="印刷厂商"
                    name="vendor"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                >
                    <Select options={[{value: '日厂', label:'日厂'}, {value: '韩厂', label:'韩厂'}, {value: '同厂', label:'同厂'}, {value: '未知', label:'未知'}]}
                    />
                </Form.Item>

                <Form.Item
                    label="入手平台"
                    name="buyPlatform"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                >
                    <Select options={ baseData['BuyPlatform'] }></Select>
                </Form.Item>

                <Form.Item
                    label="备注"
                    name="remark"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 15 }}
                >
                    <TextArea rows={3} maxLength={50} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default CardSellForm;